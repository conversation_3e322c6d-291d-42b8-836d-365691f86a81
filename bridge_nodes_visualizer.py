import math
import random
from collections import defaultdict

def read_network(filename):
    """读取网络文件"""
    edges = []
    nodes = set()
    
    with open(filename, 'r') as f:
        lines = f.readlines()
        # 第一行是节点数和边数
        num_nodes, num_edges = map(int, lines[0].strip().split())
        
        # 读取边
        for line in lines[1:]:
            if line.strip():
                u, v = map(int, line.strip().split())
                edges.append((u, v))
                nodes.add(u)
                nodes.add(v)
    
    return list(nodes), edges, num_nodes, num_edges

def calculate_degree(nodes, edges):
    """计算节点度数"""
    degree = defaultdict(int)
    for u, v in edges:
        degree[u] += 1
        degree[v] += 1
    return degree

def build_adjacency_list(nodes, edges):
    """构建邻接表"""
    adj = defaultdict(set)
    for u, v in edges:
        adj[u].add(v)
        adj[v].add(u)
    return adj

def detect_communities_simple(nodes, edges, num_communities=8):
    """简单的社区检测"""
    degree = calculate_degree(nodes, edges)
    sorted_nodes = sorted(nodes, key=lambda x: degree[x], reverse=True)
    
    communities = {}
    community_size = len(nodes) // num_communities
    
    for i, node in enumerate(sorted_nodes):
        communities[node] = i // community_size if i // community_size < num_communities else num_communities - 1
    
    return communities

def find_bridge_nodes(nodes, edges, communities):
    """找到桥节点 - 连接不同社区的节点"""
    adj = build_adjacency_list(nodes, edges)
    bridge_nodes = set()
    
    for node in nodes:
        node_community = communities[node]
        neighbor_communities = set()
        
        # 检查邻居节点的社区
        for neighbor in adj[node]:
            neighbor_community = communities[neighbor]
            if neighbor_community != node_community:
                neighbor_communities.add(neighbor_community)
        
        # 如果连接到2个或更多不同社区，则为桥节点
        if len(neighbor_communities) >= 2:
            bridge_nodes.add(node)
    
    return bridge_nodes

def calculate_betweenness_centrality_simple(nodes, edges, sample_size=50):
    """简化的介数中心性计算（采样）"""
    adj = build_adjacency_list(nodes, edges)
    betweenness = defaultdict(float)
    
    # 随机采样一些节点作为起点
    sample_nodes = random.sample(nodes, min(sample_size, len(nodes)))
    
    for source in sample_nodes:
        # BFS计算最短路径
        distances = {source: 0}
        predecessors = defaultdict(list)
        queue = [source]
        
        while queue:
            current = queue.pop(0)
            for neighbor in adj[current]:
                if neighbor not in distances:
                    distances[neighbor] = distances[current] + 1
                    queue.append(neighbor)
                    predecessors[neighbor].append(current)
                elif distances[neighbor] == distances[current] + 1:
                    predecessors[neighbor].append(current)
        
        # 计算依赖性
        dependency = defaultdict(float)
        for target in sorted(distances.keys(), key=lambda x: distances[x], reverse=True):
            if target != source:
                for pred in predecessors[target]:
                    dependency[pred] += (1 + dependency[target]) / len(predecessors[target])
        
        for node in dependency:
            betweenness[node] += dependency[node]
    
    return betweenness

def force_directed_layout(nodes, edges, width=1200, height=900, iterations=150):
    """力导向布局"""
    pos = {}
    center_x, center_y = width // 2, height // 2
    for node in nodes:
        pos[node] = [
            random.uniform(center_x - 200, center_x + 200), 
            random.uniform(center_y - 150, center_y + 150)
        ]
    
    k = math.sqrt((width * height) / len(nodes)) * 0.8
    
    for iteration in range(iterations):
        forces = {node: [0, 0] for node in nodes}
        
        # 斥力
        for i, node1 in enumerate(nodes):
            for j, node2 in enumerate(nodes):
                if i != j:
                    dx = pos[node1][0] - pos[node2][0]
                    dy = pos[node1][1] - pos[node2][1]
                    distance = math.sqrt(dx*dx + dy*dy)
                    if distance > 0 and distance < k * 3:
                        force = k*k / (distance * distance)
                        forces[node1][0] += force * dx / distance
                        forces[node1][1] += force * dy / distance
        
        # 引力
        for u, v in edges:
            dx = pos[u][0] - pos[v][0]
            dy = pos[u][1] - pos[v][1]
            distance = math.sqrt(dx*dx + dy*dy)
            if distance > 0:
                force = distance / k * 0.1
                fx = force * dx / distance
                fy = force * dy / distance
                forces[u][0] -= fx
                forces[u][1] -= fy
                forces[v][0] += fx
                forces[v][1] += fy
        
        # 向中心的引力
        for node in nodes:
            dx = pos[node][0] - center_x
            dy = pos[node][1] - center_y
            distance = math.sqrt(dx*dx + dy*dy)
            if distance > 0:
                center_force = 0.01
                forces[node][0] -= center_force * dx / distance
                forces[node][1] -= center_force * dy / distance
        
        # 更新位置
        cooling = 1.0 - iteration / iterations
        for node in nodes:
            displacement = math.sqrt(forces[node][0]**2 + forces[node][1]**2)
            if displacement > 0:
                max_displacement = min(5 * cooling, k * 0.1)
                pos[node][0] += forces[node][0] / displacement * min(displacement, max_displacement)
                pos[node][1] += forces[node][1] / displacement * min(displacement, max_displacement)
                
                margin = 80
                if pos[node][0] < margin:
                    pos[node][0] = margin + random.uniform(0, 20)
                elif pos[node][0] > width - margin:
                    pos[node][0] = width - margin - random.uniform(0, 20)
                    
                if pos[node][1] < margin:
                    pos[node][1] = margin + random.uniform(0, 20)
                elif pos[node][1] > height - margin:
                    pos[node][1] = height - margin - random.uniform(0, 20)
    
    return pos

def generate_bridge_svg(nodes, edges, pos, degree, communities, bridge_nodes, betweenness, filename):
    """生成突出桥节点的SVG"""
    width, height = 1200, 900
    
    # 社区颜色（淡化）
    community_colors = ['#ffcccb', '#add8e6', '#90ee90', '#ffd1dc', '#dda0dd', '#afeeee', '#f0e68c', '#d3d3d3']
    
    svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg width="{width}" height="{height}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- 白色背景 -->
  <rect width="{width}" height="{height}" fill="white"/>
  
  <!-- 标题 -->
  <text x="{width//2}" y="40" font-family="Arial, sans-serif" font-size="24" text-anchor="middle" fill="#2c3e50" font-weight="bold">NetScience Network - Bridge Nodes Highlighted</text>
  <text x="{width//2}" y="65" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="#7f8c8d">{len(bridge_nodes)} bridge nodes out of {len(nodes)} total nodes</text>
  
  <!-- 普通边 -->
  <g stroke="#E0E0E0" stroke-width="0.5" opacity="0.4">
'''
    
    # 添加普通边
    for u, v in edges:
        if u not in bridge_nodes and v not in bridge_nodes:
            x1, y1 = pos[u]
            x2, y2 = pos[v]
            svg_content += f'    <line x1="{x1:.1f}" y1="{y1:.1f}" x2="{x2:.1f}" y2="{y2:.1f}"/>\n'
    
    svg_content += '  </g>\n\n  <!-- 桥边（连接桥节点的边） -->\n  <g stroke="#FF6B6B" stroke-width="1.5" opacity="0.8">\n'
    
    # 添加桥边
    for u, v in edges:
        if u in bridge_nodes or v in bridge_nodes:
            x1, y1 = pos[u]
            x2, y2 = pos[v]
            svg_content += f'    <line x1="{x1:.1f}" y1="{y1:.1f}" x2="{x2:.1f}" y2="{y2:.1f}"/>\n'
    
    svg_content += '  </g>\n\n  <!-- 节点 -->\n  <g>\n'
    
    # 添加普通节点
    max_degree = max(degree.values())
    max_betweenness = max(betweenness.values()) if betweenness else 1
    
    for node in nodes:
        if node not in bridge_nodes:
            x, y = pos[node]
            node_degree = degree[node]
            radius = 4 + (node_degree / max_degree) * 8
            color = community_colors[communities[node] % len(community_colors)]
            
            svg_content += f'    <circle cx="{x:.1f}" cy="{y:.1f}" r="{radius:.1f}" fill="{color}" stroke="white" stroke-width="1" opacity="0.7"/>\n'
    
    # 添加桥节点（突出显示）
    for node in bridge_nodes:
        x, y = pos[node]
        node_degree = degree[node]
        node_betweenness = betweenness.get(node, 0)
        
        # 桥节点大小基于度数和介数中心性
        radius = 8 + (node_degree / max_degree) * 12 + (node_betweenness / max_betweenness) * 5
        
        svg_content += f'''    <circle cx="{x:.1f}" cy="{y:.1f}" r="{radius:.1f}" fill="#FF6B6B" stroke="#C0392B" stroke-width="3" filter="url(#glow)"/>
    <text x="{x:.1f}" y="{y+radius+15:.1f}" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="#2c3e50" font-weight="bold">{node}</text>
'''
    
    svg_content += '  </g>\n</svg>'
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(svg_content)

def main():
    print("读取NetScience网络数据...")
    nodes, edges, num_nodes, num_edges = read_network('netscience-int.txt')
    print(f"网络包含 {len(nodes)} 个节点和 {len(edges)} 条边")
    
    print("计算节点度数...")
    degree = calculate_degree(nodes, edges)
    
    print("检测社区结构...")
    communities = detect_communities_simple(nodes, edges)
    
    print("识别桥节点...")
    bridge_nodes = find_bridge_nodes(nodes, edges, communities)
    print(f"发现 {len(bridge_nodes)} 个桥节点")
    
    print("计算介数中心性...")
    betweenness = calculate_betweenness_centrality_simple(nodes, edges)
    
    print("计算节点布局...")
    pos = force_directed_layout(nodes, edges)
    
    print("生成桥节点可视化...")
    generate_bridge_svg(nodes, edges, pos, degree, communities, bridge_nodes, betweenness, 'bridge_nodes_network.svg')
    print("完成！生成文件: bridge_nodes_network.svg")
    
    # 输出桥节点信息
    print(f"\n桥节点列表: {sorted(bridge_nodes)}")

if __name__ == "__main__":
    main()
