<?xml version="1.0" encoding="UTF-8"?>
<svg width="1000" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradients for nodes -->
    <radialGradient id="leaderGrad" cx="30%" cy="30%">
      <stop offset="0%" stop-color="#ff6b6b"/>
      <stop offset="100%" stop-color="#e74c3c"/>
    </radialGradient>
    <radialGradient id="group1Grad" cx="30%" cy="30%">
      <stop offset="0%" stop-color="#4ecdc4"/>
      <stop offset="100%" stop-color="#26a69a"/>
    </radialGradient>
    <radialGradient id="group2Grad" cx="30%" cy="30%">
      <stop offset="0%" stop-color="#45b7d1"/>
      <stop offset="100%" stop-color="#3498db"/>
    </radialGradient>
    <radialGradient id="bridgeGrad" cx="30%" cy="30%">
      <stop offset="0%" stop-color="#f39c12"/>
      <stop offset="100%" stop-color="#e67e22"/>
    </radialGradient>

    <!-- Shadow filter -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.3"/>
    </filter>

    <!-- Glow effect -->
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- Background with gradient -->
  <defs>
    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#2c3e50"/>
      <stop offset="100%" stop-color="#34495e"/>
    </linearGradient>
  </defs>
  <rect width="1000" height="800" fill="url(#bgGrad)"/>

  <!-- Title with glow -->
  <text x="500" y="50" font-family="'Segoe UI', Arial, sans-serif" font-size="28" text-anchor="middle" fill="#ecf0f1" font-weight="bold" filter="url(#glow)">🥋 Zachary's Karate Club Network</text>

  <!-- Edges (connections) with beautiful styling -->
  <g>
    <!-- Strong connections (thick lines) -->
    <g stroke="#7fb3d3" stroke-width="3" opacity="0.8">
      <!-- Leader 1 core connections -->
      <line x1="200" y1="200" x2="280" y2="150"/>
      <line x1="200" y1="200" x2="250" y2="250"/>
      <line x1="200" y1="200" x2="150" y2="280"/>
      <line x1="200" y1="200" x2="120" y2="220"/>
      <line x1="200" y1="200" x2="300" y2="200"/>

      <!-- Leader 2 core connections -->
      <line x1="800" y1="500" x2="720" y2="450"/>
      <line x1="800" y1="500" x2="750" y2="550"/>
      <line x1="800" y1="500" x2="850" y2="450"/>
      <line x1="800" y1="500" x2="880" y2="520"/>
      <line x1="800" y1="500" x2="700" y2="500"/>
    </g>

    <!-- Medium connections -->
    <g stroke="#95a5a6" stroke-width="2" opacity="0.6">
      <!-- Group 1 internal -->
      <line x1="280" y1="150" x2="250" y2="250"/>
      <line x1="250" y1="250" x2="150" y2="280"/>
      <line x1="150" y1="280" x2="120" y2="220"/>
      <line x1="120" y1="220" x2="300" y2="200"/>
      <line x1="300" y1="200" x2="350" y2="160"/>
      <line x1="280" y1="150" x2="350" y2="160"/>

      <!-- Group 2 internal -->
      <line x1="720" y1="450" x2="750" y2="550"/>
      <line x1="750" y1="550" x2="850" y2="450"/>
      <line x1="850" y1="450" x2="880" y2="520"/>
      <line x1="880" y1="520" x2="700" y2="500"/>
      <line x1="700" y1="500" x2="650" y2="460"/>
      <line x1="720" y1="450" x2="650" y2="460"/>
    </g>

    <!-- Bridge connections (special styling) -->
    <g stroke="#f39c12" stroke-width="2" opacity="0.7" stroke-dasharray="5,5">
      <line x1="400" y1="300" x2="500" y2="350"/>
      <line x1="500" y1="350" x2="600" y2="400"/>
      <line x1="350" y1="160" x2="400" y2="300"/>
      <line x1="600" y1="400" x2="650" y2="460"/>
    </g>

    <!-- Weak connections -->
    <g stroke="#bdc3c7" stroke-width="1" opacity="0.4">
      <!-- Peripheral connections -->
      <line x1="100" y1="350" x2="150" y2="380"/>
      <line x1="150" y1="380" x2="200" y2="400"/>
      <line x1="380" y1="120" x2="420" y2="140"/>
      <line x1="420" y1="140" x2="450" y2="180"/>
      <line x1="750" y1="600" x2="800" y2="620"/>
      <line x1="800" y1="620" x2="850" y2="600"/>
      <line x1="900" y1="400" x2="920" y2="440"/>
    </g>
  </g>

  <!-- Nodes (club members) -->
  <g>
    <!-- Leader 1: Mr. Hi (node 1) -->
    <circle cx="150" cy="150" r="12" fill="#FF5722" stroke="#D84315" stroke-width="3"/>
    <text x="150" y="155" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="white" font-weight="bold">1</text>

    <!-- Group 1 members -->
    <circle cx="200" cy="120" r="8" fill="#4CAF50" stroke="#2E7D32" stroke-width="2"/>
    <text x="200" y="125" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="white" font-weight="bold">2</text>

    <circle cx="180" cy="180" r="8" fill="#4CAF50" stroke="#2E7D32" stroke-width="2"/>
    <text x="180" y="185" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="white" font-weight="bold">3</text>

    <circle cx="120" cy="200" r="8" fill="#4CAF50" stroke="#2E7D32" stroke-width="2"/>
    <text x="120" y="205" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="white" font-weight="bold">4</text>

    <circle cx="100" cy="170" r="8" fill="#4CAF50" stroke="#2E7D32" stroke-width="2"/>
    <text x="100" y="175" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="white" font-weight="bold">5</text>

    <circle cx="220" cy="160" r="8" fill="#4CAF50" stroke="#2E7D32" stroke-width="2"/>
    <text x="220" y="165" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="white" font-weight="bold">6</text>

    <circle cx="170" cy="220" r="8" fill="#4CAF50" stroke="#2E7D32" stroke-width="2"/>
    <text x="170" y="225" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="white" font-weight="bold">7</text>

    <circle cx="250" cy="140" r="8" fill="#4CAF50" stroke="#2E7D32" stroke-width="2"/>
    <text x="250" y="145" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="white" font-weight="bold">8</text>

    <circle cx="130" cy="120" r="8" fill="#4CAF50" stroke="#2E7D32" stroke-width="2"/>
    <text x="130" y="125" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="white" font-weight="bold">9</text>

    <!-- Bridge nodes -->
    <circle cx="300" cy="250" r="8" fill="#2196F3" stroke="#1976D2" stroke-width="2"/>
    <text x="300" y="255" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="white" font-weight="bold">10</text>

    <circle cx="350" cy="280" r="8" fill="#2196F3" stroke="#1976D2" stroke-width="2"/>
    <text x="350" y="285" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="white" font-weight="bold">11</text>

    <circle cx="400" cy="300" r="8" fill="#2196F3" stroke="#1976D2" stroke-width="2"/>
    <text x="400" y="305" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="white" font-weight="bold">12</text>

    <circle cx="450" cy="320" r="8" fill="#2196F3" stroke="#1976D2" stroke-width="2"/>
    <text x="450" y="325" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="white" font-weight="bold">13</text>

    <circle cx="500" cy="340" r="8" fill="#2196F3" stroke="#1976D2" stroke-width="2"/>
    <text x="500" y="345" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="white" font-weight="bold">14</text>

    <!-- Group 2 members -->
    <circle cx="600" cy="380" r="8" fill="#9C27B0" stroke="#7B1FA2" stroke-width="2"/>
    <text x="600" y="385" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="white" font-weight="bold">15</text>

    <circle cx="680" cy="370" r="8" fill="#9C27B0" stroke="#7B1FA2" stroke-width="2"/>
    <text x="680" y="375" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="white" font-weight="bold">16</text>

    <circle cx="620" cy="430" r="8" fill="#9C27B0" stroke="#7B1FA2" stroke-width="2"/>
    <text x="620" y="435" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="white" font-weight="bold">17</text>

    <circle cx="700" cy="420" r="8" fill="#9C27B0" stroke="#7B1FA2" stroke-width="2"/>
    <text x="700" y="425" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="white" font-weight="bold">18</text>

    <circle cx="630" cy="350" r="8" fill="#9C27B0" stroke="#7B1FA2" stroke-width="2"/>
    <text x="630" y="355" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="white" font-weight="bold">19</text>

    <circle cx="580" cy="410" r="8" fill="#9C27B0" stroke="#7B1FA2" stroke-width="2"/>
    <text x="580" y="415" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="white" font-weight="bold">20</text>

    <circle cx="670" cy="450" r="8" fill="#9C27B0" stroke="#7B1FA2" stroke-width="2"/>
    <text x="670" y="455" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="white" font-weight="bold">21</text>

    <!-- Leader 2: John A (node 34) -->
    <circle cx="650" cy="400" r="12" fill="#FF5722" stroke="#D84315" stroke-width="3"/>
    <text x="650" y="405" font-family="Arial, sans-serif" font-size="10" text-anchor="middle" fill="white" font-weight="bold">34</text>
  </g>

  <!-- Legend -->
  <g transform="translate(50, 500)">
    <rect x="0" y="0" width="250" height="90" fill="white" stroke="#ccc" stroke-width="1"/>
    <text x="10" y="20" font-family="Arial, sans-serif" font-size="12" font-weight="bold">图例 (Legend):</text>
    <circle cx="20" cy="35" r="8" fill="#FF5722" stroke="#D84315" stroke-width="2"/>
    <text x="35" y="40" font-family="Arial, sans-serif" font-size="10">领导者 (Leaders)</text>
    <circle cx="20" cy="55" r="6" fill="#4CAF50" stroke="#2E7D32" stroke-width="2"/>
    <text x="35" y="60" font-family="Arial, sans-serif" font-size="10">第一组成员 (Group 1)</text>
    <circle cx="20" cy="75" r="6" fill="#9C27B0" stroke="#7B1FA2" stroke-width="2"/>
    <text x="35" y="80" font-family="Arial, sans-serif" font-size="10">第二组成员 (Group 2)</text>
    <circle cx="150" cy="55" r="6" fill="#2196F3" stroke="#1976D2" stroke-width="2"/>
    <text x="165" y="60" font-family="Arial, sans-serif" font-size="10">桥接节点 (Bridge)</text>
  </g>
</svg>
