<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .node { fill: #4CAF50; stroke: #2E7D32; stroke-width: 2; }
      .node-leader { fill: #FF5722; stroke: #D84315; stroke-width: 3; }
      .edge { stroke: #666; stroke-width: 1; opacity: 0.6; }
      .node-text { font-family: Arial, sans-serif; font-size: 10px; text-anchor: middle; fill: white; font-weight: bold; }
      .title { font-family: Arial, sans-serif; font-size: 20px; text-anchor: middle; fill: #333; font-weight: bold; }
    </style>
  </defs>
  
  <!-- Title -->
  <text x="400" y="30" class="title">Zachary's Karate Club Network</text>
  
  <!-- Edges (connections between members) -->
  <g id="edges">
    <!-- Core connections around leader 1 (Mr. <PERSON>) -->
    <line x1="150" y1="150" x2="200" y2="120" class="edge"/>
    <line x1="150" y1="150" x2="180" y2="180" class="edge"/>
    <line x1="150" y1="150" x2="120" y2="200" class="edge"/>
    <line x1="150" y1="150" x2="100" y2="170" class="edge"/>
    <line x1="150" y1="150" x2="220" y2="160" class="edge"/>
    <line x1="150" y1="150" x2="170" y2="220" class="edge"/>
    <line x1="150" y1="150" x2="250" y2="140" class="edge"/>
    <line x1="150" y1="150" x2="130" y2="120" class="edge"/>
    
    <!-- Core connections around leader 34 (John A) -->
    <line x1="650" y1="400" x2="600" y2="380" class="edge"/>
    <line x1="650" y1="400" x2="680" y2="370" class="edge"/>
    <line x1="650" y1="400" x2="620" y2="430" class="edge"/>
    <line x1="650" y1="400" x2="700" y2="420" class="edge"/>
    <line x1="650" y1="400" x2="630" y2="350" class="edge"/>
    <line x1="650" y1="400" x2="580" y2="410" class="edge"/>
    <line x1="650" y1="400" x2="670" y2="450" class="edge"/>
    
    <!-- Inter-group connections -->
    <line x1="200" y1="120" x2="180" y2="180" class="edge"/>
    <line x1="180" y1="180" x2="220" y2="160" class="edge"/>
    <line x1="220" y1="160" x2="250" y2="140" class="edge"/>
    <line x1="100" y1="170" x2="120" y2="200" class="edge"/>
    <line x1="120" y1="200" x2="170" y2="220" class="edge"/>
    
    <!-- Middle connections -->
    <line x1="300" y1="250" x2="350" y2="280" class="edge"/>
    <line x1="350" y1="280" x2="400" y2="300" class="edge"/>
    <line x1="400" y1="300" x2="450" y2="320" class="edge"/>
    <line x1="450" y1="320" x2="500" y2="340" class="edge"/>
    
    <!-- Bridge connections -->
    <line x1="250" y1="140" x2="300" y2="250" class="edge"/>
    <line x1="500" y1="340" x2="580" y2="410" class="edge"/>
    
    <!-- Additional cluster connections -->
    <line x1="600" y1="380" x2="620" y2="430" class="edge"/>
    <line x1="680" y1="370" x2="700" y2="420" class="edge"/>
    <line x1="630" y1="350" x2="670" y2="450" class="edge"/>
    
    <!-- Peripheral connections -->
    <line x1="80" y1="300" x2="120" y2="320" class="edge"/>
    <line x1="120" y1="320" x2="160" y2="340" class="edge"/>
    <line x1="720" y1="200" x2="680" y2="220" class="edge"/>
    <line x1="680" y1="220" x2="640" y2="240" class="edge"/>
    
    <!-- Cross-cluster weak ties -->
    <line x1="300" y1="250" x2="580" y2="410" class="edge" style="stroke-dasharray: 3,3; opacity: 0.3;"/>
    <line x1="170" y1="220" x2="630" y2="350" class="edge" style="stroke-dasharray: 3,3; opacity: 0.3;"/>
  </g>
  
  <!-- Nodes (club members) -->
  <g id="nodes">
    <!-- Leader 1: Mr. Hi (node 1) -->
    <circle cx="150" cy="150" r="12" class="node-leader"/>
    <text x="150" y="155" class="node-text">1</text>
    
    <!-- Mr. Hi's faction -->
    <circle cx="200" cy="120" r="8" class="node"/>
    <text x="200" y="125" class="node-text">2</text>
    
    <circle cx="180" cy="180" r="8" class="node"/>
    <text x="180" y="185" class="node-text">3</text>
    
    <circle cx="120" cy="200" r="8" class="node"/>
    <text x="120" y="205" class="node-text">4</text>
    
    <circle cx="100" cy="170" r="8" class="node"/>
    <text x="100" y="175" class="node-text">5</text>
    
    <circle cx="220" cy="160" r="8" class="node"/>
    <text x="220" y="165" class="node-text">6</text>
    
    <circle cx="170" cy="220" r="8" class="node"/>
    <text x="170" y="225" class="node-text">7</text>
    
    <circle cx="250" cy="140" r="8" class="node"/>
    <text x="250" y="145" class="node-text">8</text>
    
    <circle cx="130" cy="120" r="8" class="node"/>
    <text x="130" y="125" class="node-text">9</text>
    
    <!-- Middle/Bridge nodes -->
    <circle cx="300" cy="250" r="8" class="node"/>
    <text x="300" y="255" class="node-text">10</text>
    
    <circle cx="350" cy="280" r="8" class="node"/>
    <text x="350" y="285" class="node-text">11</text>
    
    <circle cx="400" cy="300" r="8" class="node"/>
    <text x="400" y="305" class="node-text">12</text>
    
    <circle cx="450" cy="320" r="8" class="node"/>
    <text x="450" y="325" class="node-text">13</text>
    
    <circle cx="500" cy="340" r="8" class="node"/>
    <text x="500" y="345" class="node-text">14</text>
    
    <!-- Peripheral nodes -->
    <circle cx="80" cy="300" r="6" class="node"/>
    <text x="80" y="305" class="node-text">15</text>
    
    <circle cx="120" cy="320" r="6" class="node"/>
    <text x="120" y="325" class="node-text">16</text>
    
    <circle cx="160" cy="340" r="6" class="node"/>
    <text x="160" y="345" class="node-text">17</text>
    
    <circle cx="720" cy="200" r="6" class="node"/>
    <text x="720" y="205" class="node-text">18</text>
    
    <circle cx="680" cy="220" r="6" class="node"/>
    <text x="680" y="225" class="node-text">19</text>
    
    <circle cx="640" cy="240" r="6" class="node"/>
    <text x="640" y="245" class="node-text">20</text>
    
    <!-- John A's faction -->
    <circle cx="600" cy="380" r="8" class="node"/>
    <text x="600" y="385" class="node-text">21</text>
    
    <circle cx="680" cy="370" r="8" class="node"/>
    <text x="680" y="375" class="node-text">22</text>
    
    <circle cx="620" cy="430" r="8" class="node"/>
    <text x="620" y="435" class="node-text">23</text>
    
    <circle cx="700" cy="420" r="8" class="node"/>
    <text x="700" y="425" class="node-text">24</text>
    
    <circle cx="630" cy="350" r="8" class="node"/>
    <text x="630" y="355" class="node-text">25</text>
    
    <circle cx="580" cy="410" r="8" class="node"/>
    <text x="580" y="415" class="node-text">26</text>
    
    <circle cx="670" cy="450" r="8" class="node"/>
    <text x="670" y="455" class="node-text">27</text>
    
    <!-- Additional members -->
    <circle cx="550" cy="380" r="6" class="node"/>
    <text x="550" y="385" class="node-text">28</text>
    
    <circle cx="720" cy="380" r="6" class="node"/>
    <text x="720" y="385" class="node-text">29</text>
    
    <circle cx="600" cy="480" r="6" class="node"/>
    <text x="600" y="485" class="node-text">30</text>
    
    <circle cx="750" cy="420" r="6" class="node"/>
    <text x="750" y="425" class="node-text">31</text>
    
    <circle cx="560" cy="450" r="6" class="node"/>
    <text x="560" y="455" class="node-text">32</text>
    
    <circle cx="710" cy="480" r="6" class="node"/>
    <text x="710" y="485" class="node-text">33</text>
    
    <!-- Leader 2: John A (node 34) -->
    <circle cx="650" cy="400" r="12" class="node-leader"/>
    <text x="650" y="405" class="node-text">34</text>
  </g>
  
  <!-- Legend -->
  <g id="legend" transform="translate(50, 500)">
    <rect x="0" y="0" width="200" height="80" fill="none" stroke="#ccc" stroke-width="1"/>
    <text x="10" y="20" style="font-family: Arial; font-size: 12px; font-weight: bold;">Legend:</text>
    <circle cx="20" cy="35" r="8" class="node-leader"/>
    <text x="35" y="40" style="font-family: Arial; font-size: 10px;">Leaders (Mr. Hi & John A)</text>
    <circle cx="20" cy="55" r="6" class="node"/>
    <text x="35" y="60" style="font-family: Arial; font-size: 10px;">Club Members</text>
    <line x1="20" y1="70" x2="40" y2="70" class="edge"/>
    <text x="45" y="75" style="font-family: Arial; font-size: 10px;">Friendships</text>
  </g>
</svg>
