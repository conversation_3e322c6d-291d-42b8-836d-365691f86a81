<?xml version="1.0" encoding="UTF-8"?>
<svg width="1000" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradients for nodes -->
    <radialGradient id="leaderGrad" cx="30%" cy="30%">
      <stop offset="0%" stop-color="#ff6b6b"/>
      <stop offset="100%" stop-color="#e74c3c"/>
    </radialGradient>
    <radialGradient id="group1Grad" cx="30%" cy="30%">
      <stop offset="0%" stop-color="#4ecdc4"/>
      <stop offset="100%" stop-color="#26a69a"/>
    </radialGradient>
    <radialGradient id="group2Grad" cx="30%" cy="30%">
      <stop offset="0%" stop-color="#45b7d1"/>
      <stop offset="100%" stop-color="#3498db"/>
    </radialGradient>
    <radialGradient id="bridgeGrad" cx="30%" cy="30%">
      <stop offset="0%" stop-color="#f39c12"/>
      <stop offset="100%" stop-color="#e67e22"/>
    </radialGradient>

    <!-- Shadow filter -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.3"/>
    </filter>

    <!-- Glow effect -->
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- Background with gradient -->
  <defs>
    <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#2c3e50"/>
      <stop offset="100%" stop-color="#34495e"/>
    </linearGradient>
  </defs>
  <rect width="1000" height="800" fill="url(#bgGrad)"/>

  <!-- Title with glow -->
  <text x="500" y="50" font-family="'Segoe UI', Arial, sans-serif" font-size="28" text-anchor="middle" fill="#ecf0f1" font-weight="bold" filter="url(#glow)">🥋 Zachary's Karate Club Network</text>

  <!-- Edges (connections) with beautiful styling -->
  <g>
    <!-- Strong connections (thick lines) -->
    <g stroke="#7fb3d3" stroke-width="3" opacity="0.8">
      <!-- Leader 1 core connections -->
      <line x1="200" y1="200" x2="280" y2="150"/>
      <line x1="200" y1="200" x2="250" y2="250"/>
      <line x1="200" y1="200" x2="150" y2="280"/>
      <line x1="200" y1="200" x2="120" y2="220"/>
      <line x1="200" y1="200" x2="300" y2="200"/>

      <!-- Leader 2 core connections -->
      <line x1="800" y1="500" x2="720" y2="450"/>
      <line x1="800" y1="500" x2="750" y2="550"/>
      <line x1="800" y1="500" x2="850" y2="450"/>
      <line x1="800" y1="500" x2="880" y2="520"/>
      <line x1="800" y1="500" x2="700" y2="500"/>
    </g>

    <!-- Medium connections -->
    <g stroke="#95a5a6" stroke-width="2" opacity="0.6">
      <!-- Group 1 internal -->
      <line x1="280" y1="150" x2="250" y2="250"/>
      <line x1="250" y1="250" x2="150" y2="280"/>
      <line x1="150" y1="280" x2="120" y2="220"/>
      <line x1="120" y1="220" x2="300" y2="200"/>
      <line x1="300" y1="200" x2="350" y2="160"/>
      <line x1="280" y1="150" x2="350" y2="160"/>

      <!-- Group 2 internal -->
      <line x1="720" y1="450" x2="750" y2="550"/>
      <line x1="750" y1="550" x2="850" y2="450"/>
      <line x1="850" y1="450" x2="880" y2="520"/>
      <line x1="880" y1="520" x2="700" y2="500"/>
      <line x1="700" y1="500" x2="650" y2="460"/>
      <line x1="720" y1="450" x2="650" y2="460"/>
    </g>

    <!-- Bridge connections (special styling) -->
    <g stroke="#f39c12" stroke-width="2" opacity="0.7" stroke-dasharray="5,5">
      <line x1="400" y1="300" x2="500" y2="350"/>
      <line x1="500" y1="350" x2="600" y2="400"/>
      <line x1="350" y1="160" x2="400" y2="300"/>
      <line x1="600" y1="400" x2="650" y2="460"/>
    </g>

    <!-- Weak connections -->
    <g stroke="#bdc3c7" stroke-width="1" opacity="0.4">
      <!-- Peripheral connections -->
      <line x1="100" y1="350" x2="150" y2="380"/>
      <line x1="150" y1="380" x2="200" y2="400"/>
      <line x1="380" y1="120" x2="420" y2="140"/>
      <line x1="420" y1="140" x2="450" y2="180"/>
      <line x1="750" y1="600" x2="800" y2="620"/>
      <line x1="800" y1="620" x2="850" y2="600"/>
      <line x1="900" y1="400" x2="920" y2="440"/>
    </g>
  </g>

  <!-- Beautiful Nodes with gradients and shadows -->
  <g>
    <!-- Leader 1: Mr. Hi (node 1) -->
    <circle cx="200" cy="200" r="18" fill="url(#leaderGrad)" stroke="#c0392b" stroke-width="3" filter="url(#shadow)"/>
    <text x="200" y="207" font-family="'Segoe UI', Arial, sans-serif" font-size="14" text-anchor="middle" fill="white" font-weight="bold">1</text>
    <text x="200" y="235" font-family="'Segoe UI', Arial, sans-serif" font-size="10" text-anchor="middle" fill="#ecf0f1">Mr. Hi</text>

    <!-- Group 1 members (Teal theme) -->
    <circle cx="280" cy="150" r="12" fill="url(#group1Grad)" stroke="#1abc9c" stroke-width="2" filter="url(#shadow)"/>
    <text x="280" y="156" font-family="'Segoe UI', Arial, sans-serif" font-size="11" text-anchor="middle" fill="white" font-weight="bold">2</text>

    <circle cx="250" cy="250" r="12" fill="url(#group1Grad)" stroke="#1abc9c" stroke-width="2" filter="url(#shadow)"/>
    <text x="250" y="256" font-family="'Segoe UI', Arial, sans-serif" font-size="11" text-anchor="middle" fill="white" font-weight="bold">3</text>

    <circle cx="150" cy="280" r="12" fill="url(#group1Grad)" stroke="#1abc9c" stroke-width="2" filter="url(#shadow)"/>
    <text x="150" y="286" font-family="'Segoe UI', Arial, sans-serif" font-size="11" text-anchor="middle" fill="white" font-weight="bold">4</text>

    <circle cx="120" cy="220" r="12" fill="url(#group1Grad)" stroke="#1abc9c" stroke-width="2" filter="url(#shadow)"/>
    <text x="120" y="226" font-family="'Segoe UI', Arial, sans-serif" font-size="11" text-anchor="middle" fill="white" font-weight="bold">5</text>

    <circle cx="300" cy="200" r="12" fill="url(#group1Grad)" stroke="#1abc9c" stroke-width="2" filter="url(#shadow)"/>
    <text x="300" y="206" font-family="'Segoe UI', Arial, sans-serif" font-size="11" text-anchor="middle" fill="white" font-weight="bold">6</text>

    <circle cx="350" cy="160" r="10" fill="url(#group1Grad)" stroke="#1abc9c" stroke-width="2" filter="url(#shadow)"/>
    <text x="350" y="166" font-family="'Segoe UI', Arial, sans-serif" font-size="10" text-anchor="middle" fill="white" font-weight="bold">7</text>

    <circle cx="180" cy="320" r="10" fill="url(#group1Grad)" stroke="#1abc9c" stroke-width="2" filter="url(#shadow)"/>
    <text x="180" y="326" font-family="'Segoe UI', Arial, sans-serif" font-size="10" text-anchor="middle" fill="white" font-weight="bold">8</text>

    <circle cx="380" cy="120" r="10" fill="url(#group1Grad)" stroke="#1abc9c" stroke-width="2" filter="url(#shadow)"/>
    <text x="380" y="126" font-family="'Segoe UI', Arial, sans-serif" font-size="10" text-anchor="middle" fill="white" font-weight="bold">9</text>

    <!-- Bridge nodes (Golden theme) -->
    <circle cx="400" cy="300" r="14" fill="url(#bridgeGrad)" stroke="#d68910" stroke-width="2" filter="url(#shadow)"/>
    <text x="400" y="306" font-family="'Segoe UI', Arial, sans-serif" font-size="11" text-anchor="middle" fill="white" font-weight="bold">10</text>

    <circle cx="500" cy="350" r="14" fill="url(#bridgeGrad)" stroke="#d68910" stroke-width="2" filter="url(#shadow)"/>
    <text x="500" y="356" font-family="'Segoe UI', Arial, sans-serif" font-size="11" text-anchor="middle" fill="white" font-weight="bold">11</text>

    <circle cx="600" cy="400" r="14" fill="url(#bridgeGrad)" stroke="#d68910" stroke-width="2" filter="url(#shadow)"/>
    <text x="600" y="406" font-family="'Segoe UI', Arial, sans-serif" font-size="11" text-anchor="middle" fill="white" font-weight="bold">12</text>

    <!-- Group 2 members (Blue theme) -->
    <circle cx="720" cy="450" r="12" fill="url(#group2Grad)" stroke="#2980b9" stroke-width="2" filter="url(#shadow)"/>
    <text x="720" y="456" font-family="'Segoe UI', Arial, sans-serif" font-size="11" text-anchor="middle" fill="white" font-weight="bold">13</text>

    <circle cx="750" cy="550" r="12" fill="url(#group2Grad)" stroke="#2980b9" stroke-width="2" filter="url(#shadow)"/>
    <text x="750" y="556" font-family="'Segoe UI', Arial, sans-serif" font-size="11" text-anchor="middle" fill="white" font-weight="bold">14</text>

    <circle cx="850" cy="450" r="12" fill="url(#group2Grad)" stroke="#2980b9" stroke-width="2" filter="url(#shadow)"/>
    <text x="850" y="456" font-family="'Segoe UI', Arial, sans-serif" font-size="11" text-anchor="middle" fill="white" font-weight="bold">15</text>

    <circle cx="880" cy="520" r="12" fill="url(#group2Grad)" stroke="#2980b9" stroke-width="2" filter="url(#shadow)"/>
    <text x="880" y="526" font-family="'Segoe UI', Arial, sans-serif" font-size="11" text-anchor="middle" fill="white" font-weight="bold">16</text>

    <circle cx="700" cy="500" r="12" fill="url(#group2Grad)" stroke="#2980b9" stroke-width="2" filter="url(#shadow)"/>
    <text x="700" y="506" font-family="'Segoe UI', Arial, sans-serif" font-size="11" text-anchor="middle" fill="white" font-weight="bold">17</text>

    <circle cx="650" cy="460" r="10" fill="url(#group2Grad)" stroke="#2980b9" stroke-width="2" filter="url(#shadow)"/>
    <text x="650" y="466" font-family="'Segoe UI', Arial, sans-serif" font-size="10" text-anchor="middle" fill="white" font-weight="bold">18</text>

    <circle cx="780" cy="600" r="10" fill="url(#group2Grad)" stroke="#2980b9" stroke-width="2" filter="url(#shadow)"/>
    <text x="780" y="606" font-family="'Segoe UI', Arial, sans-serif" font-size="10" text-anchor="middle" fill="white" font-weight="bold">19</text>

    <circle cx="920" cy="440" r="10" fill="url(#group2Grad)" stroke="#2980b9" stroke-width="2" filter="url(#shadow)"/>
    <text x="920" y="446" font-family="'Segoe UI', Arial, sans-serif" font-size="10" text-anchor="middle" fill="white" font-weight="bold">20</text>

    <!-- Leader 2: John A (node 34) -->
    <circle cx="800" cy="500" r="18" fill="url(#leaderGrad)" stroke="#c0392b" stroke-width="3" filter="url(#shadow)"/>
    <text x="800" y="507" font-family="'Segoe UI', Arial, sans-serif" font-size="14" text-anchor="middle" fill="white" font-weight="bold">34</text>
    <text x="800" y="535" font-family="'Segoe UI', Arial, sans-serif" font-size="10" text-anchor="middle" fill="#ecf0f1">John A</text>

    <!-- Peripheral nodes -->
    <circle cx="100" cy="350" r="8" fill="#95a5a6" stroke="#7f8c8d" stroke-width="1" filter="url(#shadow)"/>
    <text x="100" y="355" font-family="'Segoe UI', Arial, sans-serif" font-size="9" text-anchor="middle" fill="white" font-weight="bold">21</text>

    <circle cx="150" cy="380" r="8" fill="#95a5a6" stroke="#7f8c8d" stroke-width="1" filter="url(#shadow)"/>
    <text x="150" y="385" font-family="'Segoe UI', Arial, sans-serif" font-size="9" text-anchor="middle" fill="white" font-weight="bold">22</text>

    <circle cx="420" cy="140" r="8" fill="#95a5a6" stroke="#7f8c8d" stroke-width="1" filter="url(#shadow)"/>
    <text x="420" y="145" font-family="'Segoe UI', Arial, sans-serif" font-size="9" text-anchor="middle" fill="white" font-weight="bold">23</text>

    <circle cx="450" cy="180" r="8" fill="#95a5a6" stroke="#7f8c8d" stroke-width="1" filter="url(#shadow)"/>
    <text x="450" y="185" font-family="'Segoe UI', Arial, sans-serif" font-size="9" text-anchor="middle" fill="white" font-weight="bold">24</text>

    <circle cx="850" cy="600" r="8" fill="#95a5a6" stroke="#7f8c8d" stroke-width="1" filter="url(#shadow)"/>
    <text x="850" y="605" font-family="'Segoe UI', Arial, sans-serif" font-size="9" text-anchor="middle" fill="white" font-weight="bold">25</text>
  </g>

  <!-- Beautiful Legend with gradients -->
  <g transform="translate(50, 650)">
    <rect x="0" y="0" width="350" height="130" fill="rgba(52, 73, 94, 0.9)" stroke="#7fb3d3" stroke-width="2" rx="10" filter="url(#shadow)"/>
    <text x="175" y="25" font-family="'Segoe UI', Arial, sans-serif" font-size="16" text-anchor="middle" fill="#ecf0f1" font-weight="bold">🥋 Network Legend</text>

    <!-- Leaders -->
    <circle cx="25" cy="45" r="12" fill="url(#leaderGrad)" stroke="#c0392b" stroke-width="2"/>
    <text x="45" y="50" font-family="'Segoe UI', Arial, sans-serif" font-size="12" fill="#ecf0f1" font-weight="bold">Leaders (Mr. Hi & John A)</text>

    <!-- Group 1 -->
    <circle cx="25" cy="70" r="10" fill="url(#group1Grad)" stroke="#1abc9c" stroke-width="2"/>
    <text x="45" y="75" font-family="'Segoe UI', Arial, sans-serif" font-size="12" fill="#ecf0f1">Group 1 Members</text>

    <!-- Group 2 -->
    <circle cx="25" cy="95" r="10" fill="url(#group2Grad)" stroke="#2980b9" stroke-width="2"/>
    <text x="45" y="100" font-family="'Segoe UI', Arial, sans-serif" font-size="12" fill="#ecf0f1">Group 2 Members</text>

    <!-- Bridge nodes -->
    <circle cx="200" cy="45" r="10" fill="url(#bridgeGrad)" stroke="#d68910" stroke-width="2"/>
    <text x="220" y="50" font-family="'Segoe UI', Arial, sans-serif" font-size="12" fill="#ecf0f1">Bridge Nodes</text>

    <!-- Peripheral -->
    <circle cx="200" cy="70" r="8" fill="#95a5a6" stroke="#7f8c8d" stroke-width="1"/>
    <text x="220" y="75" font-family="'Segoe UI', Arial, sans-serif" font-size="12" fill="#ecf0f1">Peripheral Members</text>

    <!-- Connection types -->
    <line x1="200" y1="90" x2="240" y2="90" stroke="#7fb3d3" stroke-width="3" opacity="0.8"/>
    <text x="250" y="95" font-family="'Segoe UI', Arial, sans-serif" font-size="12" fill="#ecf0f1">Strong Connections</text>

    <line x1="200" y1="105" x2="240" y2="105" stroke="#f39c12" stroke-width="2" stroke-dasharray="8,4"/>
    <text x="250" y="110" font-family="'Segoe UI', Arial, sans-serif" font-size="12" fill="#ecf0f1">Bridge Connections</text>
  </g>

  <!-- Network statistics -->
  <g transform="translate(750, 100)">
    <rect x="0" y="0" width="200" height="120" fill="rgba(52, 73, 94, 0.8)" stroke="#7fb3d3" stroke-width="1" rx="8" filter="url(#shadow)"/>
    <text x="100" y="20" font-family="'Segoe UI', Arial, sans-serif" font-size="14" text-anchor="middle" fill="#ecf0f1" font-weight="bold">Network Stats</text>
    <text x="10" y="40" font-family="'Segoe UI', Arial, sans-serif" font-size="11" fill="#ecf0f1">📊 Total Nodes: 25</text>
    <text x="10" y="55" font-family="'Segoe UI', Arial, sans-serif" font-size="11" fill="#ecf0f1">🔗 Total Edges: 35+</text>
    <text x="10" y="70" font-family="'Segoe UI', Arial, sans-serif" font-size="11" fill="#ecf0f1">👥 Groups: 2 main</text>
    <text x="10" y="85" font-family="'Segoe UI', Arial, sans-serif" font-size="11" fill="#ecf0f1">🌉 Bridge Nodes: 3</text>
    <text x="10" y="100" font-family="'Segoe UI', Arial, sans-serif" font-size="11" fill="#ecf0f1">⭐ Leaders: 2</text>
  </g>
</svg>
