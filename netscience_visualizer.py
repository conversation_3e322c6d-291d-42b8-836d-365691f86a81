import math
import random
from collections import defaultdict

def read_network(filename):
    """读取网络文件"""
    edges = []
    nodes = set()
    
    with open(filename, 'r') as f:
        lines = f.readlines()
        # 第一行是节点数和边数
        num_nodes, num_edges = map(int, lines[0].strip().split())
        
        # 读取边
        for line in lines[1:]:
            if line.strip():
                u, v = map(int, line.strip().split())
                edges.append((u, v))
                nodes.add(u)
                nodes.add(v)
    
    return list(nodes), edges, num_nodes, num_edges

def calculate_degree(nodes, edges):
    """计算节点度数"""
    degree = defaultdict(int)
    for u, v in edges:
        degree[u] += 1
        degree[v] += 1
    return degree

def force_directed_layout(nodes, edges, width=1200, height=900, iterations=100):
    """使用力导向算法计算节点位置"""
    # 初始化随机位置
    pos = {}
    for node in nodes:
        pos[node] = [random.uniform(100, width-100), random.uniform(100, height-100)]
    
    # 力导向算法参数
    k = math.sqrt((width * height) / len(nodes))  # 理想距离
    
    for iteration in range(iterations):
        # 计算斥力
        forces = {node: [0, 0] for node in nodes}
        
        for i, node1 in enumerate(nodes):
            for j, node2 in enumerate(nodes):
                if i != j:
                    dx = pos[node1][0] - pos[node2][0]
                    dy = pos[node1][1] - pos[node2][1]
                    distance = math.sqrt(dx*dx + dy*dy)
                    if distance > 0:
                        force = k*k / distance
                        forces[node1][0] += force * dx / distance
                        forces[node1][1] += force * dy / distance
        
        # 计算引力
        for u, v in edges:
            dx = pos[u][0] - pos[v][0]
            dy = pos[u][1] - pos[v][1]
            distance = math.sqrt(dx*dx + dy*dy)
            if distance > 0:
                force = distance * distance / k
                fx = force * dx / distance
                fy = force * dy / distance
                forces[u][0] -= fx
                forces[u][1] -= fy
                forces[v][0] += fx
                forces[v][1] += fy
        
        # 更新位置
        for node in nodes:
            displacement = math.sqrt(forces[node][0]**2 + forces[node][1]**2)
            if displacement > 0:
                max_displacement = min(10, k)
                pos[node][0] += forces[node][0] / displacement * min(displacement, max_displacement)
                pos[node][1] += forces[node][1] / displacement * min(displacement, max_displacement)
                
                # 边界约束
                pos[node][0] = max(50, min(width-50, pos[node][0]))
                pos[node][1] = max(50, min(height-50, pos[node][1]))
    
    return pos

def detect_communities(nodes, edges, num_communities=5):
    """简单的社区检测（基于度数和连接）"""
    degree = calculate_degree(nodes, edges)
    
    # 按度数排序节点
    sorted_nodes = sorted(nodes, key=lambda x: degree[x], reverse=True)
    
    # 简单分组
    communities = {}
    community_size = len(nodes) // num_communities
    
    for i, node in enumerate(sorted_nodes):
        communities[node] = i // community_size if i // community_size < num_communities else num_communities - 1
    
    return communities

def generate_svg(nodes, edges, pos, degree, communities, filename):
    """生成SVG文件"""
    width, height = 1200, 900
    
    # 颜色方案
    colors = ['#e74c3c', '#3498db', '#2ecc71', '#f39c12', '#9b59b6', '#1abc9c', '#e67e22', '#34495e']
    
    svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg width="{width}" height="{height}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="1" dy="1" stdDeviation="2" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- 白色背景 -->
  <rect width="{width}" height="{height}" fill="white"/>
  
  <!-- 标题 -->
  <text x="{width//2}" y="40" font-family="Arial, sans-serif" font-size="24" text-anchor="middle" fill="#2c3e50" font-weight="bold">NetScience Collaboration Network</text>
  <text x="{width//2}" y="65" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="#7f8c8d">{len(nodes)} nodes, {len(edges)} edges</text>
  
  <!-- 边 -->
  <g stroke="#bdc3c7" stroke-width="1" opacity="0.6">
'''
    
    # 添加边
    for u, v in edges:
        x1, y1 = pos[u]
        x2, y2 = pos[v]
        svg_content += f'    <line x1="{x1:.1f}" y1="{y1:.1f}" x2="{x2:.1f}" y2="{y2:.1f}"/>\n'
    
    svg_content += '  </g>\n\n  <!-- 节点 -->\n  <g>\n'
    
    # 添加节点
    max_degree = max(degree.values())
    for node in nodes:
        x, y = pos[node]
        node_degree = degree[node]
        
        # 根据度数调整节点大小
        radius = 3 + (node_degree / max_degree) * 8
        
        # 根据社区分配颜色
        color = colors[communities[node] % len(colors)]
        
        svg_content += f'''    <circle cx="{x:.1f}" cy="{y:.1f}" r="{radius:.1f}" fill="{color}" stroke="white" stroke-width="1.5" filter="url(#shadow)"/>
'''
        
        # 为高度数节点添加标签
        if node_degree > max_degree * 0.7:
            svg_content += f'    <text x="{x:.1f}" y="{y+radius+12:.1f}" font-family="Arial, sans-serif" font-size="8" text-anchor="middle" fill="#2c3e50">{node}</text>\n'
    
    svg_content += '  </g>\n\n'
    
    # 添加图例（无边框）
    legend_x, legend_y = 50, height - 180
    svg_content += f'''  <!-- 图例 -->
  <g transform="translate({legend_x}, {legend_y})">
    <text x="0" y="0" font-family="Arial, sans-serif" font-size="16" fill="#2c3e50" font-weight="bold">Legend</text>

    <text x="0" y="25" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">Node size ∝ degree</text>
    <text x="0" y="40" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">Colors = communities</text>

    <circle cx="10" cy="60" r="3" fill="{colors[0]}"/>
    <circle cx="30" cy="60" r="5" fill="{colors[1]}"/>
    <circle cx="55" cy="60" r="7" fill="{colors[2]}"/>
    <text x="70" y="65" font-family="Arial, sans-serif" font-size="10" fill="#7f8c8d">Low → High degree</text>

    <line x1="10" y1="80" x2="50" y2="80" stroke="#bdc3c7" stroke-width="1"/>
    <text x="60" y="85" font-family="Arial, sans-serif" font-size="10" fill="#7f8c8d">Collaborations</text>

    <text x="0" y="105" font-family="Arial, sans-serif" font-size="10" fill="#7f8c8d">High-degree nodes labeled</text>
  </g>

  <!-- 统计信息（无边框） -->
  <g transform="translate({width-220}, 100)">
    <text x="0" y="0" font-family="Arial, sans-serif" font-size="16" fill="#2c3e50" font-weight="bold">Network Stats</text>
    <text x="0" y="25" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">Max degree: {max_degree}</text>
    <text x="0" y="40" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">Avg degree: {sum(degree.values())/len(nodes):.1f}</text>
    <text x="0" y="55" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">Density: {2*len(edges)/(len(nodes)*(len(nodes)-1)):.3f}</text>
    <text x="0" y="70" font-family="Arial, sans-serif" font-size="12" fill="#2c3e50">Communities: {len(set(communities.values()))}</text>
  </g>
</svg>'''
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(svg_content)

def main():
    print("读取NetScience网络数据...")
    nodes, edges, num_nodes, num_edges = read_network('netscience-int.txt')
    print(f"网络包含 {len(nodes)} 个节点和 {len(edges)} 条边")
    
    print("计算节点度数...")
    degree = calculate_degree(nodes, edges)
    
    print("检测社区结构...")
    communities = detect_communities(nodes, edges)
    
    print("计算节点布局...")
    pos = force_directed_layout(nodes, edges)
    
    print("生成SVG可视化...")
    generate_svg(nodes, edges, pos, degree, communities, 'netscience_network.svg')
    print("完成！生成文件: netscience_network.svg")

if __name__ == "__main__":
    main()
