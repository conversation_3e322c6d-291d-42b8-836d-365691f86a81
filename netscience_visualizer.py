import math
import random
from collections import defaultdict

def read_network(filename):
    """读取网络文件"""
    edges = []
    nodes = set()
    
    with open(filename, 'r') as f:
        lines = f.readlines()
        # 第一行是节点数和边数
        num_nodes, num_edges = map(int, lines[0].strip().split())
        
        # 读取边
        for line in lines[1:]:
            if line.strip():
                u, v = map(int, line.strip().split())
                edges.append((u, v))
                nodes.add(u)
                nodes.add(v)
    
    return list(nodes), edges, num_nodes, num_edges

def calculate_degree(nodes, edges):
    """计算节点度数"""
    degree = defaultdict(int)
    for u, v in edges:
        degree[u] += 1
        degree[v] += 1
    return degree

def force_directed_layout(nodes, edges, width=1200, height=900, iterations=150):
    """使用力导向算法计算节点位置"""
    # 初始化随机位置（更集中在中心区域）
    pos = {}
    center_x, center_y = width // 2, height // 2
    for node in nodes:
        # 在中心区域随机分布
        pos[node] = [
            random.uniform(center_x - 200, center_x + 200),
            random.uniform(center_y - 150, center_y + 150)
        ]

    # 力导向算法参数
    k = math.sqrt((width * height) / len(nodes)) * 0.8  # 理想距离

    for iteration in range(iterations):
        # 计算斥力
        forces = {node: [0, 0] for node in nodes}

        for i, node1 in enumerate(nodes):
            for j, node2 in enumerate(nodes):
                if i != j:
                    dx = pos[node1][0] - pos[node2][0]
                    dy = pos[node1][1] - pos[node2][1]
                    distance = math.sqrt(dx*dx + dy*dy)
                    if distance > 0 and distance < k * 3:  # 只在近距离计算斥力
                        force = k*k / (distance * distance)
                        forces[node1][0] += force * dx / distance
                        forces[node1][1] += force * dy / distance

        # 计算引力
        for u, v in edges:
            dx = pos[u][0] - pos[v][0]
            dy = pos[u][1] - pos[v][1]
            distance = math.sqrt(dx*dx + dy*dy)
            if distance > 0:
                force = distance / k * 0.1  # 减小引力
                fx = force * dx / distance
                fy = force * dy / distance
                forces[u][0] -= fx
                forces[u][1] -= fy
                forces[v][0] += fx
                forces[v][1] += fy

        # 添加向中心的轻微引力
        for node in nodes:
            dx = pos[node][0] - center_x
            dy = pos[node][1] - center_y
            distance = math.sqrt(dx*dx + dy*dy)
            if distance > 0:
                center_force = 0.01
                forces[node][0] -= center_force * dx / distance
                forces[node][1] -= center_force * dy / distance

        # 更新位置
        cooling = 1.0 - iteration / iterations  # 冷却因子
        for node in nodes:
            displacement = math.sqrt(forces[node][0]**2 + forces[node][1]**2)
            if displacement > 0:
                max_displacement = min(5 * cooling, k * 0.1)
                pos[node][0] += forces[node][0] / displacement * min(displacement, max_displacement)
                pos[node][1] += forces[node][1] / displacement * min(displacement, max_displacement)

                # 柔性边界约束（不强制贴边）
                margin = 80
                if pos[node][0] < margin:
                    pos[node][0] = margin + random.uniform(0, 20)
                elif pos[node][0] > width - margin:
                    pos[node][0] = width - margin - random.uniform(0, 20)

                if pos[node][1] < margin:
                    pos[node][1] = margin + random.uniform(0, 20)
                elif pos[node][1] > height - margin:
                    pos[node][1] = height - margin - random.uniform(0, 20)

    return pos

def detect_communities(nodes, edges, num_communities=5):
    """简单的社区检测（基于度数和连接）"""
    degree = calculate_degree(nodes, edges)
    
    # 按度数排序节点
    sorted_nodes = sorted(nodes, key=lambda x: degree[x], reverse=True)
    
    # 简单分组
    communities = {}
    community_size = len(nodes) // num_communities
    
    for i, node in enumerate(sorted_nodes):
        communities[node] = i // community_size if i // community_size < num_communities else num_communities - 1
    
    return communities

def generate_svg(nodes, edges, pos, degree, communities, filename):
    """生成SVG文件"""
    width, height = 1200, 900
    
    # 颜色方案
    colors = ['#e74c3c', '#3498db', '#2ecc71', '#ff6b9d', '#9b59b6', '#1abc9c', '#00d2d3', '#34495e']
    
    svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg width="{width}" height="{height}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="1" dy="1" stdDeviation="2" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- 白色背景 -->
  <rect width="{width}" height="{height}" fill="white"/>
  
  <!-- 标题 -->
  <text x="{width//2}" y="40" font-family="Arial, sans-serif" font-size="24" text-anchor="middle" fill="#2c3e50" font-weight="bold">NetScience Collaboration Network</text>
  <text x="{width//2}" y="65" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="#7f8c8d">{len(nodes)} nodes, {len(edges)} edges</text>
  
  <!-- 边 -->
  <g stroke="#E6E6FA" stroke-width="0.5" opacity="0.8">
'''
    
    # 添加边
    for u, v in edges:
        x1, y1 = pos[u]
        x2, y2 = pos[v]
        svg_content += f'    <line x1="{x1:.1f}" y1="{y1:.1f}" x2="{x2:.1f}" y2="{y2:.1f}"/>\n'
    
    svg_content += '  </g>\n\n  <!-- 节点 -->\n  <g>\n'
    
    # 添加节点
    max_degree = max(degree.values())
    for node in nodes:
        x, y = pos[node]
        node_degree = degree[node]
        
        # 根据度数调整节点大小（增大所有节点）
        radius = 6 + (node_degree / max_degree) * 15
        
        # 根据社区分配颜色
        color = colors[communities[node] % len(colors)]
        
        svg_content += f'''    <circle cx="{x:.1f}" cy="{y:.1f}" r="{radius:.1f}" fill="{color}" stroke="white" stroke-width="1.5" filter="url(#shadow)"/>
'''
    
    svg_content += '  </g>\n</svg>'
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(svg_content)

def main():
    print("读取NetScience网络数据...")
    nodes, edges, num_nodes, num_edges = read_network('netscience-int.txt')
    print(f"网络包含 {len(nodes)} 个节点和 {len(edges)} 条边")
    
    print("计算节点度数...")
    degree = calculate_degree(nodes, edges)
    
    print("检测社区结构...")
    communities = detect_communities(nodes, edges)
    
    print("计算节点布局...")
    pos = force_directed_layout(nodes, edges)
    
    print("生成SVG可视化...")
    generate_svg(nodes, edges, pos, degree, communities, 'netscience_network.svg')
    print("完成！生成文件: netscience_network.svg")

if __name__ == "__main__":
    main()
